import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AnalyticsComponent } from './analytics.component';
import { GoogleAnalyticsComponent } from './google-analytics/google-analytics.component';
import { BingAnalyticsComponent } from './bing-analytics/bing-analytics.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'google',
    pathMatch: 'full',
  },
  {
    path: 'google',
    component: GoogleAnalyticsComponent,
  },
  {
    path: 'bing',
    component: BingAnalyticsComponent,
  },
  {
    path: 'legacy',
    component: AnalyticsComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AnalyticsRouting {}
