import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AnalyticsComponent } from './analytics.component';
import { GoogleAnalyticsComponent } from './google-analytics/google-analytics.component';
import { GoogleInsightsModule } from '../google-insights/google-insights.module';
import { SharedModule } from '../shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { MatTab, MatTabGroup, MatTabLabel } from '@angular/material/tabs';
import { MatIcon } from '@angular/material/icon';
import { BingInsightsModule } from '../bing-insights/bing-insights.module';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { AnalyticsRouting } from './analytics.routing';

@NgModule({
  declarations: [AnalyticsComponent, GoogleAnalyticsComponent],
  imports: [
    CommonModule,
    AnalyticsRouting,
    GoogleInsightsModule,
    SharedModule,
    TranslateModule,
    MatTabGroup,
    MatTab,
    MatIcon,
    MatTabLabel,
    BingInsightsModule,
    GalaxyLoadingSpinnerModule
  ],
  exports: [AnalyticsComponent, GoogleAnalyticsComponent]
})
export class AnalyticsModule {}
