<app-page [pageTitle]="pageTitleKey$ | async | translate">
  @if (upgradeToProEnabled$ | async) {
    <div class="bing-analytics-header">
      <span class="tab-label-spacer"></span>
      <img [src]="bingIconUrl" alt="Bing Icon" width="24" height="24" class="tab-icon" />
      <span class="tab-label-text">{{ 'BING_INSIGHTS.TITLE' | translate }}</span>
      <span class="tab-label-extra-spacer"></span>
    </div>
    <br />
    @if (isFreeEdition$ | async) {
      <app-bing-empty-states
        [upgradeToProEnabled]="upgradeToProEnabled$ | async"
        [isStandardUser]="true"
        [isBingSyncEnable$]="isBingSyncEnable$"
        [isBingListingAvailable$]="isBingListingAvailable$"
      ></app-bing-empty-states>
    } @else {
      @if ((isBingDataAvailable$ | async) && (isBingSyncEnable$ | async)) {
        <app-bing-insights></app-bing-insights>
      } @else {
        <app-bing-empty-states
          [upgradeToProEnabled]="upgradeToProEnabled$ | async"
          [isStandardUser]="false"
          [isBingSyncEnable$]="isBingSyncEnable$"
          [isBingListingAvailable$]="isBingListingAvailable$"
        ></app-bing-empty-states>
      }
    }
  } @else {
    <div class="access-denied">
      <p>{{ 'BING_INSIGHTS.ACCESS_DENIED' | translate }}</p>
    </div>
  }
</app-page>
