import { Component, Inject, OnInit } from '@angular/core';
import { Observable, of } from 'rxjs';
import { catchError, map, shareReplay, switchMap, take } from 'rxjs/operators';
import { AGIDTOKEN, appID } from '@vendasta/local-seo';
import { AppSettings, EditionUpgradeAction } from '@vendasta/marketplace-apps';
import {
  IsLocalSEOProActiveForAccountRequest,
  IsLocalSEOProActiveForAccountResponse,
  ListingProductsApiService,
} from '@vendasta/listing-products';
import { AccountGroupApiService, GetMultiRequest, ProjectionFilter } from '@vendasta/account-group';
import { AppPartnerService } from '@galaxy/marketplace-apps';
import { ProductAnalyticsService } from '@vendasta/product-analytics';

@Component({
  selector: 'app-google-analytics',
  templateUrl: './google-analytics.component.html',
  styleUrls: ['./google-analytics.component.scss'],
  standalone: false,
})
export class GoogleAnalyticsComponent implements OnInit {
  pageTitleKey$ = of('GOOGLE_INSIGHTS.NAME');
  readonly googleIconUrl = 'https://www.cdnstyles.com/static/images/source-icons/Google.svg';
  upgradeToProEnabled$: Observable<boolean>;
  public isFreeEdition$: Observable<boolean>;

  constructor(
    @Inject(AGIDTOKEN) private agid$: Observable<string>,
    private listingProducts: ListingProductsApiService,
    private accountGroupService: AccountGroupApiService,
    private appPartnerService: AppPartnerService,
    private productAnalyticsService: ProductAnalyticsService,
  ) {}

  ngOnInit(): void {
    this.initializeIsFreeEdition$();

    this.upgradeToProEnabled$ = this.isFreeEdition$.pipe(
      switchMap((isFreeEdition) => {
        if (isFreeEdition) {
          return this.upgradeToProAppSettings$().pipe(map((settings) => !settings.editionChange.hideUpgradeCta));
        }
        return of(true);
      }),
    );

    this.productAnalyticsService.trackEvent('google-insight-tab', 'listing-builder-client', 'onload', 0);
  }

  private upgradeToProAppSettings$(): Observable<AppSettings> {
    const defaultAppSettings = new AppSettings({
      editionChange: {
        editionUpgradeAction: EditionUpgradeAction.EDITION_UPGRADE_ACTION_CONTACT_SALES,
      },
      branding: { enabled: false },
    });

    const partnerMarket$ = this.agid$.pipe(
      switchMap((businessID) =>
        businessID
          ? this.accountGroupService
              .getMulti(
                new GetMultiRequest({
                  accountGroupIds: [businessID],
                  projectionFilter: new ProjectionFilter({
                    accountGroupExternalIdentifiers: true,
                  }),
                }),
              )
              .pipe(
                map((resp) => {
                  const identifiers = resp.accountGroups[0]?.accountGroup?.accountGroupExternalIdentifiers;
                  const partnerId = identifiers?.partnerId;
                  const marketId = identifiers?.marketId;
                  return { partnerId, marketId };
                }),
              )
          : of(null),
      ),
      shareReplay(1),
    );

    return partnerMarket$.pipe(
      switchMap((pm) =>
        pm
          ? this.appPartnerService.getAppSettings(appID, pm.partnerId, pm.marketId).pipe(
              catchError((err) => {
                if (err.status === 404) {
                  return of(defaultAppSettings);
                }
                throw err;
              }),
              map((response) => response || defaultAppSettings),
            )
          : of(defaultAppSettings),
      ),
      shareReplay(1),
    );
  }

  private initializeIsFreeEdition$(): void {
    this.isFreeEdition$ = this.agid$.pipe(
      take(1),
      switchMap((accountGroupId) =>
        this.listingProducts.isLocalSeoProActiveForAccount(
          new IsLocalSEOProActiveForAccountRequest({ accountGroupId }),
        ),
      ),
      map((response: IsLocalSEOProActiveForAccountResponse) => !response.isActive),
    );
  }
}
